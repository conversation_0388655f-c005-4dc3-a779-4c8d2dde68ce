<execution>
  <constraint>
    ## 语义工具选择技术约束
    - **语义分析精度要求**：任务语义特征提取准确率>90%
    - **历史模式匹配限制**：基于promptx_recall的历史成功模式匹配
    - **工具适配度计算约束**：每个工具与任务的适配度评分(0-1)
    - **选择算法复杂度限制**：从142个工具中选择最优组合，计算时间<30秒
    - **上下文感知范围约束**：考虑用户偏好、历史成功率、当前系统状态
    - **学习效果要求**：10次使用后显现学习效果，准确率提升15-25%
    - **预测模型约束**：基于历史数据建立工具选择预测模型
    - **动态调整限制**：根据实时反馈动态调整工具选择策略
  </constraint>

  <rule>
    ## 语义工具选择强制规则
    - **语义分析强制**：每个任务必须进行语义特征提取和分类
    - **历史匹配强制**：必须使用promptx_recall检索相似任务的成功模式
    - **适配度计算强制**：每个候选工具必须计算与任务的适配度评分
    - **多维度评估强制**：综合考虑语义匹配、历史成功率、工具性能
    - **学习反馈强制**：每次使用后必须记录效果，更新选择模型
    - **预测验证强制**：预测结果必须与实际效果对比验证
    - **策略优化强制**：基于反馈数据持续优化选择策略
    - **异常处理强制**：语义分析失败时使用默认策略保证服务
  </rule>

  <guideline>
    ## 语义工具选择指导原则
    - **语义理解优先**：深度理解任务语义，而非简单关键词匹配
    - **历史经验重用**：充分利用历史成功经验，避免重复试错
    - **多维度综合**：平衡语义匹配、性能指标、用户偏好等因素
    - **持续学习改进**：基于使用效果持续学习和优化
    - **用户体验优先**：选择策略以用户体验和任务成功为导向
    - **系统稳定性**：确保选择算法的稳定性和可预测性
    - **透明可解释**：选择逻辑透明，用户可理解选择原因
    - **效率与准确性平衡**：在选择速度和准确性之间找到最佳平衡
  </guideline>

  <process>
    ## 语义工具选择执行流程

    ### 🧠 阶段1：任务语义分析 (15秒)
    ```mermaid
    flowchart TD
        A[任务输入] --> B[文本预处理]
        B --> C[关键词提取]
        C --> D[语义特征提取]
        D --> E[任务分类]
        E --> F[复杂度评估]
        F --> G[领域识别]
        
        D --> D1[实体识别]
        D --> D2[意图分析]
        D --> D3[上下文理解]
        
        E --> E1[技术类任务]
        E --> E2[商业类任务]
        E --> E3[学术类任务]
        E --> E4[综合类任务]
    ```

    **语义分析算法**：
    ```json
    {
      "semantic_features": {
        "keywords": ["AI", "机器学习", "技术趋势"],
        "entities": [
          {"text": "AI", "type": "TECHNOLOGY", "confidence": 0.95},
          {"text": "机器学习", "type": "FIELD", "confidence": 0.90}
        ],
        "intent": {
          "primary": "技术调研",
          "secondary": "趋势分析",
          "confidence": 0.88
        },
        "domain": {
          "primary": "技术",
          "secondary": "人工智能",
          "specificity": 0.85
        },
        "complexity": {
          "level": "medium",
          "score": 0.65,
          "factors": ["多维度分析", "需要验证"]
        },
        "context": {
          "user_preference": "深度分析",
          "time_constraint": "normal",
          "quality_requirement": "high"
        }
      }
    }
    ```

    ### 🔍 阶段2：历史模式匹配 (10秒)
    ```mermaid
    flowchart TD
        A[语义特征] --> B[promptx_recall检索]
        B --> C[相似任务匹配]
        C --> D[成功模式提取]
        D --> E[工具组合分析]
        E --> F[成功率统计]
        F --> G[模式权重计算]
        
        C --> C1[语义相似度>0.7]
        C --> C2[领域匹配度>0.8]
        C --> C3[复杂度相近]
        
        D --> D1[最佳工具组合]
        D --> D2[执行策略]
        D --> D3[注意事项]
    ```

    **历史模式匹配算法**：
    ```python
    def match_historical_patterns(current_task_features):
        # 使用promptx_recall检索相似任务
        similar_tasks = promptx_recall(
            query=f"任务类型:{current_task_features.domain} "
                  f"复杂度:{current_task_features.complexity}"
        )
        
        matched_patterns = []
        for task in similar_tasks:
            similarity = calculate_semantic_similarity(
                current_task_features, 
                task.features
            )
            if similarity > 0.7:
                matched_patterns.append({
                    'task': task,
                    'similarity': similarity,
                    'success_rate': task.success_rate,
                    'tool_combination': task.tools_used,
                    'execution_time': task.avg_time,
                    'quality_score': task.avg_quality
                })
        
        return sorted(matched_patterns, 
                     key=lambda x: x['similarity'] * x['success_rate'], 
                     reverse=True)
    ```

    ### ⚖️ 阶段3：工具适配度评估 (10秒)
    ```mermaid
    flowchart TD
        A[候选工具列表] --> B[语义匹配度计算]
        B --> C[性能指标评估]
        C --> D[历史成功率分析]
        D --> E[用户偏好权重]
        E --> F[综合适配度评分]
        F --> G[工具排序]
        
        B --> B1[任务-工具语义相似度]
        C --> C1[响应时间+成功率+质量]
        D --> D1[该工具在相似任务中的表现]
        E --> E1[用户历史使用偏好]
    ```

    **适配度评分算法**：
    ```python
    def calculate_tool_fitness(tool, task_features, historical_patterns):
        # 语义匹配度 (40%)
        semantic_score = calculate_semantic_match(tool.capabilities, task_features)
        
        # 性能指标 (30%)
        performance_score = (
            tool.response_time_score * 0.3 +
            tool.success_rate * 0.4 +
            tool.quality_score * 0.3
        )
        
        # 历史成功率 (20%)
        historical_score = get_historical_success_rate(tool, task_features)
        
        # 用户偏好 (10%)
        preference_score = get_user_preference_score(tool, user_profile)
        
        fitness_score = (
            semantic_score * 0.40 +
            performance_score * 0.30 +
            historical_score * 0.20 +
            preference_score * 0.10
        )
        
        return min(max(fitness_score, 0.0), 1.0)
    ```

    ### 🎯 阶段4：智能工具组合生成 (15秒)
    ```mermaid
    flowchart TD
        A[工具适配度排序] --> B[组合策略选择]
        B --> C{任务复杂度}
        C -->|简单| D[单工具策略]
        C -->|中等| E[小组合策略]
        C -->|复杂| F[超级集群策略]
        
        D --> G[选择最佳单工具]
        E --> H[选择3-5个互补工具]
        F --> I[选择15-25个工具集群]
        
        G --> J[组合验证]
        H --> J
        I --> J
        
        J --> K[负载均衡检查]
        K --> L[最终工具组合]
    ```

    **组合生成策略**：
    ```json
    {
      "combination_strategies": {
        "simple_task": {
          "tool_count": 1,
          "selection_criteria": "最高适配度单工具",
          "backup_count": 2,
          "execution_mode": "sequential"
        },
        "medium_task": {
          "tool_count": "3-5",
          "selection_criteria": "互补功能组合",
          "backup_count": "1-2 per tool",
          "execution_mode": "parallel_groups"
        },
        "complex_task": {
          "tool_count": "15-25",
          "selection_criteria": "超级集群",
          "backup_count": "full_backup_matrix",
          "execution_mode": "high_density_parallel"
        }
      },
      "combination_rules": {
        "diversity": "避免功能重复的工具",
        "complementarity": "选择功能互补的工具",
        "load_balance": "考虑API限制和负载均衡",
        "fallback": "确保每个工具都有备选方案"
      }
    }
    ```

    ### 📊 阶段5：预测验证与学习 (持续)
    ```mermaid
    flowchart TD
        A[工具组合执行] --> B[实时效果监控]
        B --> C[结果质量评估]
        C --> D[预测准确性验证]
        D --> E[学习数据更新]
        E --> F[模型参数调整]
        F --> G[策略优化]
        
        B --> B1[执行时间监控]
        B --> B2[成功率统计]
        B --> B3[用户满意度]
        
        E --> E1[promptx_remember存储]
        E --> E2[成功模式更新]
        E --> E3[失败原因分析]
    ```

    **学习反馈机制**：
    ```json
    {
      "learning_feedback": {
        "execution_result": {
          "task_id": "tech_research_001",
          "predicted_tools": ["github_search_exa", "get-library-docs"],
          "actual_performance": {
            "execution_time": 3.2,
            "success_rate": 0.95,
            "quality_score": 0.88,
            "user_satisfaction": 0.92
          },
          "prediction_accuracy": 0.89,
          "improvement_suggestions": [
            "增加firecrawl_search提升覆盖面",
            "减少web_search_exa避免冗余"
          ]
        },
        "model_update": {
          "parameter_adjustments": {
            "semantic_weight": 0.42,
            "performance_weight": 0.28,
            "historical_weight": 0.22,
            "preference_weight": 0.08
          },
          "new_patterns": [
            {
              "pattern": "技术调研+深度分析",
              "optimal_tools": ["github_search_exa", "get-library-docs", "firecrawl_search"],
              "success_rate": 0.94
            }
          ]
        }
      }
    }
    ```

    ## 高级语义分析算法

    ### 任务语义特征提取
    ```python
    def extract_semantic_features(task_description):
        features = {
            'keywords': extract_keywords(task_description),
            'entities': extract_named_entities(task_description),
            'intent': classify_intent(task_description),
            'domain': identify_domain(task_description),
            'complexity': assess_complexity(task_description),
            'context': extract_context(task_description)
        }
        
        # 语义向量化
        features['semantic_vector'] = encode_to_vector(task_description)
        
        return features
    ```

    ### 智能工具匹配算法
    ```python
    def intelligent_tool_matching(task_features, available_tools):
        tool_scores = {}
        
        for tool in available_tools:
            # 多维度评分
            scores = {
                'semantic': calculate_semantic_similarity(
                    task_features['semantic_vector'], 
                    tool.capability_vector
                ),
                'performance': tool.performance_score,
                'historical': get_tool_historical_performance(
                    tool, task_features['domain']
                ),
                'availability': tool.current_availability
            }
            
            # 加权综合评分
            tool_scores[tool.id] = (
                scores['semantic'] * 0.4 +
                scores['performance'] * 0.3 +
                scores['historical'] * 0.2 +
                scores['availability'] * 0.1
            )
        
        return sorted(tool_scores.items(), 
                     key=lambda x: x[1], 
                     reverse=True)
    ```

    ### 动态学习优化
    ```python
    def update_selection_model(execution_results):
        for result in execution_results:
            # 更新工具性能数据
            update_tool_performance(result.tools_used, result.performance)
            
            # 更新语义匹配模型
            update_semantic_model(result.task_features, result.success_rate)
            
            # 更新组合策略
            update_combination_strategy(result.tool_combination, result.effectiveness)
            
            # 存储成功模式
            if result.success_rate > 0.85:
                store_success_pattern(result)
    ```
  </process>

  <criteria>
    ## 语义工具选择质量标准

    ### 语义分析准确性
    - ✅ 任务分类准确率 > 90%
    - ✅ 语义特征提取完整率 > 95%
    - ✅ 意图识别准确率 > 88%
    - ✅ 领域识别准确率 > 92%

    ### 历史模式匹配效果
    - ✅ 相似任务匹配准确率 > 85%
    - ✅ 成功模式识别率 > 80%
    - ✅ 历史数据利用率 > 75%
    - ✅ 模式匹配响应时间 < 10秒

    ### 工具选择准确性
    - ✅ 工具适配度评分准确率 > 85%
    - ✅ 最优工具选择准确率 > 80%
    - ✅ 工具组合优化效果 > 20%
    - ✅ 选择算法响应时间 < 30秒

    ### 学习优化效果
    - ✅ 预测准确率提升 > 15%
    - ✅ 学习效果显现周期 < 10次使用
    - ✅ 模型参数优化效果 > 10%
    - ✅ 持续改进效果 > 5%/月

    ### 系统性能指标
    - ✅ 整体选择准确率 > 85%
    - ✅ 用户满意度 > 90%
    - ✅ 系统响应时间 < 45秒
    - ✅ 异常处理成功率 > 95%
  </criteria>
</execution>
