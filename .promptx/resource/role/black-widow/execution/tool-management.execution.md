<execution>
  <constraint>
    ## 工具管理核心约束
    - **三集群架构**：技术集群15-20工具，商业集群10-15工具，学术集群8-12工具
    - **性能评级要求**：A级工具优先，B级备选，C级应急，动态调整评级
    - **负载均衡限制**：智能分组并行，避免API限制和资源竞争
    - **健康监控约束**：实时监控工具状态，异常自动降级切换
    - **兼容性要求**：所有工具必须与PromptX系统完全兼容
  </constraint>

  <rule>
    ## 工具超级集群强制规则
    - **集群自动选择**：根据任务语义特征自动选择对应超级集群
    - **工具优先级执行**：A级工具优先，B级备选，C级应急使用
    - **并行分组强制**：同集群工具智能分为3-5个并行组执行
    - **性能监控强制**：实时监控工具性能，动态调整优先级
    - **备选切换强制**：主工具失败自动切换备选，保证任务完成
    - **效果评估强制**：每次使用后评估工具效果，更新性能评级
    - **组合优化强制**：基于历史数据优化工具组合，提升整体效果
    - **资源管理强制**：智能管理API调用频率，避免触发限制
  </rule>

  <guideline>
    ## 工具超级集群指导原则
    - **专业化优先**：每个集群专注特定领域，提升专业性
    - **互补性设计**：集群内工具功能互补，覆盖完整工作流
    - **可扩展性**：集群设计支持新工具加入，保持架构灵活
    - **效率最大化**：通过智能组合最大化信息获取效率
    - **质量保证**：多工具交叉验证，确保信息准确性
    - **用户友好**：自动化程度高，用户无需了解技术细节
    - **持续优化**：基于使用效果持续优化集群配置
    - **稳定可靠**：核心工具稳定，备选机制完善
  </guideline>

  <process>
    ## 工具超级集群配置与调度

    ### 🔧 技术情报超级集群 (20-25工具)
    ```mermaid
    mindmap
      root((技术情报集群))
        GitHub矩阵
          search_repositories_github(A级)
          search_code_github(A级)
          search_issues_github(B级)
          github_search_exa(A级)
          get_file_contents_github(B级)
          list_commits_github(C级)
        文档全覆盖
          get-library-docs(A级)
          deepwiki_fetch(A级)
          convert_to_markdown(B级)
          firecrawl_extract(A级)
          codebase-retrieval(A级)
        搜索矩阵
          firecrawl_search(A级)
          tavily_search(A级)
          web_search_exa(B级)
          brave_web_search(C级)
        深度研究
          firecrawl_deep_research(A级)
          deep_researcher_start_exa(A级)
          firecrawl_crawl(B级)
          git-commit-retrieval(B级)
          sequentialthinking(A级)
    ```

    **技术集群工具配置**：
    - **核心搜索组** (并行组1)：github_search_exa + search_repositories_github + search_code_github
    - **文档提取组** (并行组2)：get-library-docs + deepwiki_fetch + codebase-retrieval
    - **内容分析组** (并行组3)：firecrawl_extract + convert_to_markdown + firecrawl_search
    - **深度研究组** (并行组4)：firecrawl_deep_research + deep_researcher_start_exa + sequentialthinking
    - **验证补充组** (并行组5)：tavily_search + firecrawl_crawl + git-commit-retrieval

    ### 💼 商业情报超级集群 (15-18工具)
    ```mermaid
    mindmap
      root((商业情报集群))
        企业研究
          company_research_exa(A级)
          linkedin_search_exa(A级)
          search_users_github(B级)
        全网搜索
          firecrawl_search(A级)
          tavily_search(A级)
          brave_web_search(B级)
          web-search(B级)
        内容提取
          firecrawl_extract(A级)
          tavily_extract(A级)
          web-fetch(B级)
          crawling_exa(B级)
        深度分析
          firecrawl_deep_research(A级)
          firecrawl_crawl(B级)
          tavily_crawl(B级)
          firecrawl_map(C级)
        验证工具
          sequentialthinking(A级)
          promptx_think(B级)
    ```

    **商业集群工具配置**：
    - **企业调研组** (并行组1)：company_research_exa + linkedin_search_exa + search_users_github
    - **信息搜索组** (并行组2)：firecrawl_search + tavily_search + brave_web_search
    - **内容提取组** (并行组3)：firecrawl_extract + tavily_extract + web-fetch
    - **深度分析组** (并行组4)：firecrawl_deep_research + sequentialthinking + promptx_think
    - **验证映射组** (并行组5)：firecrawl_crawl + tavily_crawl + firecrawl_map

    ### 📚 学术研究超级集群 (10-12工具)
    ```mermaid
    mindmap
      root((学术研究集群))
        学术搜索
          research_paper_search_exa(A级)
          wikipedia_search_exa(A级)
          firecrawl_search(A级)
        文档处理
          convert_to_markdown(A级)
          firecrawl_extract(A级)
          deepwiki_fetch(B级)
        深度研究
          firecrawl_deep_research(A级)
          deep_researcher_start_exa(A级)
          sequentialthinking(A级)
        验证工具
          tavily_search(B级)
          web-fetch(B级)
          promptx_think(B级)
    ```

    **学术集群工具配置**：
    - **学术搜索组** (并行组1)：research_paper_search_exa + wikipedia_search_exa + firecrawl_search
    - **文档处理组** (并行组2)：convert_to_markdown + firecrawl_extract + deepwiki_fetch
    - **深度研究组** (并行组3)：firecrawl_deep_research + deep_researcher_start_exa + sequentialthinking
    - **验证补充组** (并行组4)：tavily_search + web-fetch + promptx_think

    ### 🌐 综合情报全工具矩阵 (30+工具)
    ```mermaid
    flowchart TD
        A[综合情报需求] --> B[技术集群20-25工具]
        A --> C[商业集群15-18工具]
        A --> D[学术集群10-12工具]
        
        B --> E[智能负载均衡]
        C --> E
        D --> E
        
        E --> F[分批并行执行]
        F --> G[结果智能聚合]
        G --> H[综合情报报告]
    ```

    **全矩阵执行策略**：
    - **阶段1**：技术集群核心工具 (8-10工具并行)
    - **阶段2**：商业集群核心工具 (6-8工具并行)
    - **阶段3**：学术集群核心工具 (4-6工具并行)
    - **阶段4**：补充验证工具 (5-8工具并行)
    - **阶段5**：深度分析工具 (3-5工具并行)

    ## 工具性能评级与动态调整

    ### A级工具 (核心主力)
    - **搜索类**：firecrawl_search, tavily_search, github_search_exa
    - **提取类**：firecrawl_extract, get-library-docs, codebase-retrieval
    - **分析类**：sequentialthinking, firecrawl_deep_research, deep_researcher_start_exa
    - **专业类**：company_research_exa, linkedin_search_exa, research_paper_search_exa

    ### B级工具 (重要备选)
    - **搜索类**：web_search_exa, brave_web_search, wikipedia_search_exa
    - **提取类**：tavily_extract, convert_to_markdown, deepwiki_fetch
    - **分析类**：promptx_think, firecrawl_crawl, git-commit-retrieval
    - **专业类**：search_code_github, search_issues_github, web-fetch

    ### C级工具 (应急补充)
    - **搜索类**：crawling_exa, search_users_github
    - **提取类**：web-fetch, firecrawl_map
    - **分析类**：tavily_crawl, list_commits_github
    - **专业类**：get_file_contents_github

    ## 智能调度算法

    ### 工具选择决策矩阵
    ```mermaid
    graph TD
        A[任务输入] --> B[语义分析]
        B --> C{任务类型}
        C -->|技术| D[技术集群权重0.8]
        C -->|商业| E[商业集群权重0.8]
        C -->|学术| F[学术集群权重0.8]
        C -->|综合| G[全集群权重0.6]
        
        D --> H[工具性能评级]
        E --> H
        F --> H
        G --> H
        
        H --> I[历史成功率]
        I --> J[负载均衡检查]
        J --> K[最终工具组合]
    ```

    ### 动态调整机制
    1. **性能监控**：实时监控工具响应时间、成功率、结果质量
    2. **评级更新**：每周根据性能数据更新工具评级
    3. **组合优化**：基于历史数据优化工具组合效果
    4. **负载均衡**：动态调整工具调用频率，避免API限制
    5. **异常处理**：工具失败自动降级，备选工具无缝切换
  </process>

  <criteria>
    ## 工具超级集群质量标准

    ### 集群配置质量
    - ✅ 技术集群工具数量 20-25个
    - ✅ 商业集群工具数量 15-18个
    - ✅ 学术集群工具数量 10-12个
    - ✅ 工具分类准确率 > 95%

    ### 调度效率指标
    - ✅ 集群选择准确率 > 90%
    - ✅ 工具组合优化效果 > 20%
    - ✅ 并行执行成功率 > 95%
    - ✅ 负载均衡效果 > 85%

    ### 工具性能指标
    - ✅ A级工具成功率 > 95%
    - ✅ B级工具成功率 > 90%
    - ✅ C级工具成功率 > 80%
    - ✅ 备选切换成功率 > 95%

    ### 动态优化效果
    - ✅ 性能评级准确率 > 90%
    - ✅ 组合优化提升 > 15%
    - ✅ 异常处理成功率 > 95%
    - ✅ 持续改进效果 > 5%/月
  </criteria>
</execution>
