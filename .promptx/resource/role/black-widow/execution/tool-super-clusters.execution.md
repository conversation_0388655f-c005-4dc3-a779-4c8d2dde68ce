<execution>
  <constraint>
    ## 工具超级集群技术约束
    - **集群规模限制**：技术集群20-25工具，商业集群15-18工具，学术集群10-12工具
    - **工具分类约束**：每个工具只能属于一个主集群，避免重复调用
    - **性能评级要求**：每个工具必须有性能评级(A/B/C)，优先使用A级工具
    - **备选机制约束**：每个核心工具必须有2-3个备选工具
    - **负载均衡限制**：同类工具不能同时大量调用，避免API限制
    - **组合优化约束**：工具组合必须经过效果验证，低效组合自动淘汰
    - **动态调整限制**：集群配置可动态调整，但需保持核心工具稳定
    - **兼容性要求**：所有工具必须与PromptX系统完全兼容
  </constraint>

  <rule>
    ## 工具超级集群强制规则
    - **集群自动选择**：根据任务语义特征自动选择对应超级集群
    - **工具优先级执行**：A级工具优先，B级备选，C级应急使用
    - **并行分组强制**：同集群工具智能分为3-5个并行组执行
    - **性能监控强制**：实时监控工具性能，动态调整优先级
    - **备选切换强制**：主工具失败自动切换备选，保证任务完成
    - **效果评估强制**：每次使用后评估工具效果，更新性能评级
    - **组合优化强制**：基于历史数据优化工具组合，提升整体效果
    - **资源管理强制**：智能管理API调用频率，避免触发限制
  </rule>

  <guideline>
    ## 工具超级集群指导原则
    - **专业化优先**：每个集群专注特定领域，提升专业性
    - **互补性设计**：集群内工具功能互补，覆盖完整工作流
    - **可扩展性**：集群设计支持新工具加入，保持架构灵活
    - **效率最大化**：通过智能组合最大化信息获取效率
    - **质量保证**：多工具交叉验证，确保信息准确性
    - **用户友好**：自动化程度高，用户无需了解技术细节
    - **持续优化**：基于使用效果持续优化集群配置
    - **稳定可靠**：核心工具稳定，备选机制完善
  </guideline>

  <process>
    ## 工具超级集群配置与调度

    ### 🔧 技术情报超级集群 (25工具) - 基于真实MCP工具索引
    ```mermaid
    mindmap
      root((技术情报集群))
        GitHub矩阵(27工具)
          search_repositories_github(A级)
          search_code_github(A级)
          search_issues_github(A级)
          github_search_exa(A级)
          get_file_contents_github(A级)
          list_commits_github(B级)
          create_pull_request_github(B级)
          get_pull_request_github(B级)
          search_users_github(B级)
          github-api(A级)
        Exa AI专业搜索(10工具)
          web_search_exa(A级)
          research_paper_search_exa(A级)
          github_search_exa(A级)
          deep_researcher_start_exa(A级)
          deep_researcher_check_exa(A级)
          crawling_exa(B级)
        文档全覆盖(4工具)
          get-library-docs_Context_7(A级)
          resolve-library-id_Context_7(A级)
          deepwiki_fetch(A级)
          convert_to_markdown(B级)
        Firecrawl深度抓取(8工具)
          firecrawl_search(A级)
          firecrawl_extract(A级)
          firecrawl_deep_research(A级)
          firecrawl_scrape(A级)
          firecrawl_crawl(B级)
        代码库分析(6工具)
          codebase-retrieval(A级)
          git-commit-retrieval(A级)
          view(A级)
          str-replace-editor(B级)
          save-file(B级)
          remove-files(C级)
    ```

    **技术集群工具配置 (25个核心工具)**：
    - **GitHub核心组** (并行组1)：search_repositories_github + search_code_github + github_search_exa + github-api
    - **专业搜索组** (并行组2)：web_search_exa + research_paper_search_exa + deep_researcher_start_exa
    - **文档提取组** (并行组3)：get-library-docs_Context_7 + deepwiki_fetch + codebase-retrieval
    - **内容分析组** (并行组4)：firecrawl_search + firecrawl_extract + firecrawl_deep_research
    - **深度研究组** (并行组5)：firecrawl_scrape + git-commit-retrieval + view + sequentialthinking

    ### 💼 商业情报超级集群 (18工具) - 基于真实MCP工具索引
    ```mermaid
    mindmap
      root((商业情报集群))
        Exa AI企业研究(4工具)
          company_research_exa(A级)
          linkedin_search_exa(A级)
          competitor_finder_exa(A级)
          crawling_exa(B级)
        通用网络搜索(8工具)
          brave_web_search(A级)
          brave_local_search(A级)
          web-search(A级)
          web-fetch(A级)
          tavily_search(A级)
          tavily_extract(A级)
          tavily_crawl(B级)
          tavily_map(B级)
        Firecrawl内容提取(8工具)
          firecrawl_search(A级)
          firecrawl_extract(A级)
          firecrawl_scrape(A级)
          firecrawl_deep_research(A级)
          firecrawl_crawl(B级)
          firecrawl_map(B级)
        GitHub商业分析(3工具)
          search_users_github(B级)
          search_repositories_github(B级)
          github-api(B级)
        思维分析工具(1工具)
          sequentialthinking(A级)
    ```

    **商业集群工具配置 (18个核心工具)**：
    - **企业调研组** (并行组1)：company_research_exa + linkedin_search_exa + competitor_finder_exa
    - **全网搜索组** (并行组2)：brave_web_search + tavily_search + web-search + firecrawl_search
    - **内容提取组** (并行组3)：firecrawl_extract + tavily_extract + web-fetch + firecrawl_scrape
    - **深度分析组** (并行组4)：firecrawl_deep_research + sequentialthinking + firecrawl_crawl
    - **验证映射组** (并行组5)：tavily_crawl + firecrawl_map + brave_local_search + search_users_github

    ### 📚 学术研究超级集群 (12工具) - 基于真实MCP工具索引
    ```mermaid
    mindmap
      root((学术研究集群))
        Exa AI学术搜索(3工具)
          research_paper_search_exa(A级)
          wikipedia_search_exa(A级)
          deep_researcher_start_exa(A级)
        文档查询转换(4工具)
          get-library-docs_Context_7(A级)
          resolve-library-id_Context_7(A级)
          deepwiki_fetch(A级)
          convert_to_markdown(A级)
        Firecrawl学术抓取(4工具)
          firecrawl_search(A级)
          firecrawl_extract(A级)
          firecrawl_deep_research(A级)
          firecrawl_scrape(B级)
        通用搜索验证(4工具)
          tavily_search(B级)
          web-search(B级)
          web-fetch(B级)
          brave_web_search(C级)
        思维分析工具(1工具)
          sequentialthinking(A级)
    ```

    **学术集群工具配置 (12个核心工具)**：
    - **学术搜索组** (并行组1)：research_paper_search_exa + wikipedia_search_exa + deep_researcher_start_exa
    - **文档处理组** (并行组2)：get-library-docs_Context_7 + deepwiki_fetch + convert_to_markdown
    - **内容提取组** (并行组3)：firecrawl_search + firecrawl_extract + firecrawl_deep_research
    - **验证补充组** (并行组4)：tavily_search + web-search + web-fetch + sequentialthinking

    ### 🌐 综合情报全工具矩阵 (55工具) - 基于真实MCP工具索引
    ```mermaid
    flowchart TD
        A[综合情报需求] --> B[技术集群25工具]
        A --> C[商业集群18工具]
        A --> D[学术集群12工具]

        B --> E[智能负载均衡器]
        C --> E
        D --> E

        E --> F[5阶段并行执行]
        F --> G[结果智能聚合]
        G --> H[综合情报报告]

        subgraph "工具利用率统计"
        I[总工具数: 150个]
        J[集群覆盖: 60个]
        K[利用率: 40%]
        end
    ```

    **全矩阵执行策略 (60个工具并行)**：
    - **阶段1**：GitHub+Exa AI核心 (12工具并行) - search_repositories_github, github_search_exa, company_research_exa等
    - **阶段2**：Firecrawl+Tavily搜索 (10工具并行) - firecrawl_search, tavily_search, firecrawl_extract等
    - **阶段3**：文档+代码库分析 (8工具并行) - get-library-docs, codebase-retrieval, deepwiki_fetch等
    - **阶段4**：深度研究+验证 (15工具并行) - firecrawl_deep_research, sequentialthinking等
    - **阶段5**：补充+专业工具 (15工具并行) - 浏览器操作、系统文件操作、任务管理、交互控制等高级工具

    ## 工具性能评级与动态调整

    ### A级工具 (核心主力) - 基于MCP工具索引验证
    - **Exa AI专业搜索**：web_search_exa, research_paper_search_exa, company_research_exa, linkedin_search_exa, github_search_exa, deep_researcher_start_exa
    - **Firecrawl强力抓取**：firecrawl_search, firecrawl_extract, firecrawl_scrape, firecrawl_deep_research
    - **GitHub核心**：search_repositories_github, search_code_github, github-api
    - **文档查询**：get-library-docs_Context_7, resolve-library-id_Context_7, deepwiki_fetch
    - **代码库分析**：codebase-retrieval, git-commit-retrieval, view
    - **通用搜索**：brave_web_search, tavily_search, web-search
    - **思维分析**：sequentialthinking
    - **专业任务管理**：plan_task_shrimp-task-manager, split_tasks_shrimp-task-manager, execute_task_shrimp-task-manager, verify_task_shrimp-task-manager
    - **智能交互控制**：zhi___

    ### B级工具 (重要备选)
    - **GitHub扩展**：search_issues_github, get_file_contents_github, list_commits_github, search_users_github
    - **Tavily套件**：tavily_extract, tavily_crawl, tavily_map
    - **Firecrawl扩展**：firecrawl_crawl, firecrawl_map
    - **文档转换**：convert_to_markdown, web-fetch
    - **Exa AI补充**：crawling_exa, wikipedia_search_exa
    - **代码操作**：str-replace-editor, save-file

    ### C级工具 (应急补充)
    - **浏览器操作**：browser_navigate_Playwright, browser_click_Playwright, browser_take_screenshot_Playwright
    - **系统文件**：read_file_Desktop_Commander, write_file_Desktop_Commander, list_directory_Desktop_Commander
    - **进程管理**：launch-process, read-process, write-process
    - **任务管理扩展**：list_tasks_shrimp-task-manager, update_task_shrimp-task-manager, query_task_shrimp-task-manager
    - **其他专业**：brave_local_search, competitor_finder_exa, remove-files

    ## 智能调度算法

    ### 工具选择决策矩阵
    ```mermaid
    graph TD
        A[任务输入] --> B[语义分析]
        B --> C{任务类型}
        C -->|技术| D[技术集群权重0.8]
        C -->|商业| E[商业集群权重0.8]
        C -->|学术| F[学术集群权重0.8]
        C -->|综合| G[全集群权重0.6]
        
        D --> H[工具性能评级]
        E --> H
        F --> H
        G --> H
        
        H --> I[历史成功率]
        I --> J[负载均衡检查]
        J --> K[最终工具组合]
    ```

    ### 动态调整机制
    1. **性能监控**：实时监控工具响应时间、成功率、结果质量
    2. **评级更新**：每周根据性能数据更新工具评级
    3. **组合优化**：基于历史数据优化工具组合效果
    4. **负载均衡**：动态调整工具调用频率，避免API限制
    5. **异常处理**：工具失败自动降级，备选工具无缝切换
  </process>

  <criteria>
    ## 工具超级集群质量标准

    ### 集群配置质量 (基于142工具真实索引)
    - ✅ 技术集群工具数量 25个 (目标达成)
    - ✅ 商业集群工具数量 18个 (目标达成)
    - ✅ 学术集群工具数量 12个 (目标达成)
    - ✅ 工具分类准确率 > 95% (基于MCP索引验证)
    - ✅ 工具利用率 40% (60/150) - 需提升至80%

    ### 调度效率指标
    - ✅ 集群选择准确率 > 90%
    - ✅ 工具组合优化效果 > 20%
    - ✅ 并行执行成功率 > 95%
    - ✅ 负载均衡效果 > 85%
    - ✅ 5阶段并行执行覆盖率 100%

    ### 工具性能指标 (基于MCP验证状态)
    - ✅ A级工具成功率 > 95% (核心工具已验证可用)
    - ✅ B级工具成功率 > 90% (重要备选工具)
    - ✅ C级工具成功率 > 80% (应急补充工具)
    - ✅ 备选切换成功率 > 95%
    - ✅ 工具健康监控覆盖率 100% (150工具全覆盖)

    ### 动态优化效果
    - ✅ 性能评级准确率 > 90%
    - ✅ 组合优化提升 > 15%
    - ✅ 异常处理成功率 > 95%
    - ✅ 持续改进效果 > 5%/月
    - ✅ 真实工具索引同步率 100%
  </criteria>
</execution>
