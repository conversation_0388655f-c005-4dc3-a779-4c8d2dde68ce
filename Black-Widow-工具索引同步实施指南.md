# Black Widow 工具索引同步实施指南

## 🎯 实施概述

基于最新MCP工具索引（150个可用工具+6个禁用工具），对Black Widow角色进行全面升级，重点解决工具配置滞后、禁用工具使用、专业工具缺失等问题。

## 📋 实施步骤

### Phase 1: 紧急修复（立即执行）

#### 1.1 更新核心配置文件

**文件**：`.promptx/resource/role/black-widow/black-widow.role.md`

**修改内容**：
```markdown
# 第20行：更新工具总数
- **高密度并行执行**：60个核心工具智能编排，工具利用率40%，目标80%+

# 第24行：更新监控范围  
- **动态健康监控**：150个工具100%覆盖监控，异常自动降级，备选无缝切换

# 第37行：更新集群配置
- **工具超级集群强制**：技术集群25工具、商业集群18工具、学术集群12工具，基于150工具索引精确配置
```

#### 1.2 禁用工具合规性修复

**文件**：所有execution文件

**查找替换**：
- `add_tasks` → `split_tasks_shrimp-task-manager`
- `update_tasks` → `update_task_shrimp-task-manager`
- `view_tasklist` → `list_tasks_shrimp-task-manager`
- `promptx_think` → `process_thought_shrimp-task-manager`

#### 1.3 工具超级集群数量更新

**文件**：`.promptx/resource/role/black-widow/execution/tool-super-clusters.execution.md`

**修改位置**：第178-183行
```mermaid
subgraph "工具利用率统计"
I[总工具数: 150个]
J[集群覆盖: 60个]  
K[利用率: 40%]
end
```

### Phase 2: 专业工具集成（本周完成）

#### 2.1 创建工具合规性管理

**新建文件**：`.promptx/resource/role/black-widow/execution/tool-compliance.execution.md`

**内容要点**：
- 禁用工具清单和替代方案
- 工具调用前合规性检查
- 违规处理和纠正机制

#### 2.2 集成shrimp-task-manager

**修改文件**：`.promptx/resource/role/black-widow/execution/intelligence-workflow.execution.md`

**新增阶段**：
```markdown
#### 阶段0：任务规划（TASK_PLANNING）
- **需求分析**：`plan_task_shrimp-task-manager`
- **任务分解**：`split_tasks_shrimp-task-manager`  
- **执行指导**：`execute_task_shrimp-task-manager`
- **质量验证**：`verify_task_shrimp-task-manager`
```

#### 2.3 集成zhi___交互控制

**修改位置**：所有涉及用户交互的地方

**替换内容**：
```markdown
# 原来的简单输出
- **结构化输出**：直接输出结果

# 修改为智能交互
- **智能交互**：`zhi___` 专业交互，支持预定义选项、自由输入和图片上传
- **用户确认**：重要决策前通过zhi___收集用户反馈
```

### Phase 3: 性能优化（持续改进）

#### 3.1 工具利用率重新计算

**当前状态**：
- 工具总数：150个（可用）
- 集群覆盖：60个
- 利用率：40%

**目标状态**：
- 集群覆盖：120个
- 利用率：80%

#### 3.2 健康监控扩展

**文件**：`.promptx/resource/role/black-widow/execution/tool-health-monitor.execution.md`

**更新内容**：
- 监控范围从142个扩展到150个工具
- 新增shrimp-task-manager工具健康检查
- 新增zhi___交互工具状态监控

## 🔧 具体修改操作

### 操作1: 批量文本替换

**目标文件**：所有.execution.md文件

**替换清单**：
```bash
# 工具数量更新
"142个工具" → "150个工具"
"142工具" → "150工具"

# 利用率更新  
"38.7%" → "40%"
"55个核心工具" → "60个核心工具"

# 禁用工具替换
"add_tasks" → "split_tasks_shrimp-task-manager"
"update_tasks" → "update_task_shrimp-task-manager"
"view_tasklist" → "list_tasks_shrimp-task-manager"
"promptx_think" → "process_thought_shrimp-task-manager"
```

### 操作2: 新增文件创建

#### 文件1: tool-compliance.execution.md
**位置**：`.promptx/resource/role/black-widow/execution/`
**用途**：工具合规性管理

#### 文件2: task-management-integration.execution.md  
**位置**：`.promptx/resource/role/black-widow/execution/`
**用途**：专业任务管理集成

#### 文件3: interaction-control.execution.md
**位置**：`.promptx/resource/role/black-widow/execution/`
**用途**：智能交互控制

### 操作3: 引用关系更新

**文件**：`.promptx/resource/role/black-widow/black-widow.role.md`

**新增引用**：
```markdown
<principle>
  @!execution://tool-compliance
  @!execution://task-management-integration  
  @!execution://interaction-control
  # ... 其他现有引用
</principle>
```

## ✅ 验收标准

### 功能验收
- [ ] 所有禁用工具已替换为专业工具
- [ ] shrimp-task-manager完全集成
- [ ] zhi___交互控制正常工作
- [ ] 工具数量统计准确（150个）

### 性能验收
- [ ] 工具利用率达到40%（短期目标）
- [ ] 健康监控覆盖150个工具
- [ ] 任务管理流程优化
- [ ] 交互体验提升

### 质量验收
- [ ] 所有文档同步更新
- [ ] 配置文件一致性检查
- [ ] 向后兼容性验证
- [ ] 错误处理完善

## ⚠️ 注意事项

1. **备份现有配置**：修改前备份所有相关文件
2. **分步骤验证**：每个阶段完成后进行功能验证
3. **用户培训**：新交互方式需要用户适应
4. **监控指标**：密切关注性能指标变化

## 📊 预期收益

### 短期收益（1周内）
- ✅ 消除禁用工具使用风险
- ✅ 提升任务管理专业性
- ✅ 改善用户交互体验

### 中期收益（1月内）
- ✅ 工具利用率提升至40%
- ✅ 系统稳定性增强
- ✅ 分析效率提升15%

### 长期收益（3月内）
- ✅ 工具利用率达到80%目标
- ✅ 智能化程度显著提升
- ✅ 用户满意度提升30%

---

**文档版本**：v1.0
**创建时间**：2025-08-01
**负责人**：Black Widow
**预计完成**：2025-08-08
